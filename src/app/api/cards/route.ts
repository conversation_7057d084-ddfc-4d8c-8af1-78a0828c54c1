import { NextRequest, NextResponse } from "next/server";
import clientPromise from "../../../lib/mongodb";
import { ObjectId } from "mongodb";

export async function GET(request: NextRequest) {
  try {
    const client = await clientPromise;
    const db = client.db();

    const { searchParams } = new URL(request.url);
    const search = searchParams.get("search") || "";
    const cardSet = searchParams.get("cardSet") || "all";

    let filter: any = {};

    if (search) {
      filter.$or = [
        { "name.th": { $regex: search, $options: "i" } },
        { "name.en": { $regex: search, $options: "i" } },
        { "cardSet.th": { $regex: search, $options: "i" } },
        { "cardSet.en": { $regex: search, $options: "i" } },
      ];
    }

    if (cardSet !== "all") {
      filter.$and = filter.$and || [];
      filter.$and.push({
        $or: [{ "cardSet.th": cardSet }, { "cardSet.en": cardSet }],
      });
    }

    const cards = await db.collection("cards").find(filter).toArray();

    return NextResponse.json({ success: true, data: cards });
  } catch (error) {
    console.error("Error fetching cards:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch cards" },
      { status: 500 }
    );
  }
}

// Helper function to validate URL
function isValidUrl(url: string): boolean {
  if (!url || url.trim() === '') return true; // Allow empty URLs
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

export async function POST(request: NextRequest) {
  try {
    const client = await clientPromise;
    const db = client.db();

    const body = await request.json();

    // Validate image URL
    if (body.imageUrl && !isValidUrl(body.imageUrl)) {
      return NextResponse.json(
        { success: false, error: "Invalid image URL provided" },
        { status: 400 }
      );
    }

    const cardData = {
      name: {
        th: body.name_th,
        en: body.name_en,
      },
      cardSet: {
        th: body.cardSet_th,
        en: body.cardSet_en,
      },
      imageUrl: body.imageUrl || "",
      description: body.descriptions || [],
      prediction: body.predictions || [],
    };

    const result = await db.collection("cards").insertOne(cardData);

    return NextResponse.json({
      success: true,
      data: { _id: result.insertedId, ...cardData },
    });
  } catch (error) {
    console.error("Error creating card:", error);
    return NextResponse.json(
      { success: false, error: "Failed to create card" },
      { status: 500 }
    );
  }
}
