import { NextRequest, NextResponse } from "next/server";
import clientPromise from "../../../../lib/mongodb";
import { ObjectId } from "mongodb";

// Helper function to validate URL
function isValidUrl(url: string): boolean {
  if (!url || url.trim() === '') return true; // Allow empty URLs
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const client = await clientPromise;
    const db = client.db();

    const body = await request.json();
    const { id } = params;

    // Validate image URL
    if (body.imageUrl && !isValidUrl(body.imageUrl)) {
      return NextResponse.json(
        { success: false, error: "Invalid image URL provided" },
        { status: 400 }
      );
    }

    const cardData = {
      name: {
        th: body.name_th,
        en: body.name_en,
      },
      cardSet: {
        th: body.cardSet_th,
        en: body.cardSet_en,
      },
      imageUrl: body.imageUrl || "",
      description: body.descriptions || [],
      prediction: body.predictions || [],
    };

    const result = await db
      .collection("cards")
      .updateOne({ _id: new ObjectId(id) }, { $set: cardData });

    if (result.matchedCount === 0) {
      return NextResponse.json(
        { success: false, error: "Card not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: { _id: id, ...cardData },
    });
  } catch (error) {
    console.error("Error updating card:", error);
    return NextResponse.json(
      { success: false, error: "Failed to update card" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const client = await clientPromise;
    const db = client.db();

    const { id } = params;

    const result = await db
      .collection("cards")
      .deleteOne({ _id: new ObjectId(id) });

    if (result.deletedCount === 0) {
      return NextResponse.json(
        { success: false, error: "Card not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting card:", error);
    return NextResponse.json(
      { success: false, error: "Failed to delete card" },
      { status: 500 }
    );
  }
}
