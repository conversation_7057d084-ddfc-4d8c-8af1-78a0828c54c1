export interface LocalizedText {
  th: string;
  en: string;
}

export interface Description {
  category: LocalizedText;
  content: LocalizedText;
  colorCode: string;
}

export interface Prediction {
  category: LocalizedText;
  content: LocalizedText;
  colorCode: string;
}

export interface Card {
  _id: string;
  name: LocalizedText;
  cardSet: LocalizedText;
  imageUrl: string;
  description: Description[];
  prediction: Prediction[];
}

export interface CardFormData {
  name_th: string;
  name_en: string;
  cardSet_th: string;
  cardSet_en: string;
  imageUrl: string;
  descriptions: Description[];
  predictions: Prediction[];
}
