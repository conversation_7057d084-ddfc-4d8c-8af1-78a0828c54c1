// Script to fix invalid URLs in the database
// Run this with: node scripts/fix-invalid-urls.js

const { MongoClient } = require('mongodb');

// Helper function to validate URL
function isValidUrl(url) {
  if (!url || url.trim() === '') return false;
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

async function fixInvalidUrls() {
  const uri = process.env.MONGODB_URI;
  
  if (!uri) {
    console.error('MONGODB_URI environment variable is not set');
    process.exit(1);
  }

  const client = new MongoClient(uri);

  try {
    await client.connect();
    console.log('Connected to MongoDB');

    const db = client.db();
    const collection = db.collection('cards');

    // Find all cards
    const cards = await collection.find({}).toArray();
    console.log(`Found ${cards.length} cards`);

    let fixedCount = 0;

    for (const card of cards) {
      if (card.imageUrl && !isValidUrl(card.imageUrl)) {
        console.log(`Fixing invalid URL for card ${card._id}: "${card.imageUrl}"`);
        
        // Set imageUrl to empty string for invalid URLs
        await collection.updateOne(
          { _id: card._id },
          { $set: { imageUrl: "" } }
        );
        
        fixedCount++;
      }
    }

    console.log(`Fixed ${fixedCount} cards with invalid URLs`);

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await client.close();
    console.log('Disconnected from MongoDB');
  }
}

// Load environment variables if .env.local exists
try {
  require('dotenv').config({ path: '.env.local' });
} catch (e) {
  // dotenv not available, that's okay
}

fixInvalidUrls();
